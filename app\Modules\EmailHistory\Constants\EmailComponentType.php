<?php

namespace App\Modules\EmailHistory\Constants;

final class EmailComponentType
{
    // Component type constants
    public const PAYMENT_INVOICE = 'PaymentInvoice';
    public const THIRD_EXPIRATION = 'ThirdExpiration';
    public const AUTHENTICATION_REQUEST = 'AuthenticationRequest';
    public const TRANSFER_REFUND = 'TransferRefund';
    public const EXPIRATION_NOTICE = 'ExpirationNotice';
    public const IDENTITY_VERIFICATION_IN_PROCESS = 'IdentityVerificationInProcess';
    public const IDENTITY_VERIFICATION_VERIFIED = 'IdentityVerificationVerified';
    public const OTP_VERIFICATION = 'OTPVerification';
    public const ADD_ACCOUNT_CREDIT = 'AddAccountCredit';
    public const BANK_TRANSFER_NOTIFICATION = 'BankTransferNotification';
    public const DOMAIN_REDEMPTION_NOTICE = 'DomainRedemptionNotice';
    public const REPORT_ABUSE = 'ReportAbuse';
    public const CLIENTS_QUERY = 'ClientsQuery';
    public const DOMAIN_TRANSFER_REQUEST_INITIATED = 'DomainTransferRequestInitiated';
    public const USER_INVITE = 'UserInvite';
    public const DOMAIN_REDEMPTION_PERIOD = 'DomainRedemptionPeriod';
    public const DOMAIN_REGISTRATION_REFUND = 'DomainRegistrationRefund';
    public const DEFAULT = 'Default';

    public const EMAIL_TYPE_MAPPING = [
        'Registration Payment Invoice' => self::PAYMENT_INVOICE,
        'Transfer Payment Invoice' => self::PAYMENT_INVOICE,
        'Renewal Payment Invoice' => self::PAYMENT_INVOICE,
        
        'Third Expiration Notice' => self::THIRD_EXPIRATION,
        'First Expiration Notice' => self::EXPIRATION_NOTICE,
        'Second Expiration Notice' => self::EXPIRATION_NOTICE,
        
        'Domain Authentication Request' => self::AUTHENTICATION_REQUEST,
        'Domain Transfer Refund' => self::TRANSFER_REFUND,
        'Domain Transfer - Outbound Request' => self::DOMAIN_TRANSFER_REQUEST_INITIATED,
        
        'In Process Identity Verification Notice' => self::IDENTITY_VERIFICATION_IN_PROCESS,
        'Verified Identity Verification Notice' => self::IDENTITY_VERIFICATION_VERIFIED,
        
        'OTP Verification' => self::OTP_VERIFICATION,
        'Account Credit - Added to Account' => self::ADD_ACCOUNT_CREDIT,
        'Account Credit - Bank Transfer Notification' => self::BANK_TRANSFER_NOTIFICATION,
        
        'Domain Redemption Notice' => self::DOMAIN_REDEMPTION_NOTICE,
        'Domain Redemption Period' => self::DOMAIN_REDEMPTION_PERIOD,

        'Domain Registration Refund' => self::DOMAIN_REGISTRATION_REFUND,
        
        'Report Abuse' => self::REPORT_ABUSE,
        'Clients Query' => self::CLIENTS_QUERY,
        'User Invite' => self::USER_INVITE,
    ];


    public static function getComponentType(string $emailType): string
    {
        if (isset(self::EMAIL_TYPE_MAPPING[$emailType])) {
            return self::EMAIL_TYPE_MAPPING[$emailType];
        }

        if (str_contains($emailType, 'Payment Invoice')) {
            return self::PAYMENT_INVOICE;
        }
        
        if (str_contains($emailType, 'Authentication Request')) {
            return self::AUTHENTICATION_REQUEST;
        }
        
        if (str_contains($emailType, 'Transfer Refund')) {
            return self::TRANSFER_REFUND;
        }
        
        if (str_starts_with($emailType, 'First Expiration Notice') || 
            str_starts_with($emailType, 'Second Expiration Notice')) {
            return self::EXPIRATION_NOTICE;
        }

        return self::DEFAULT;
    }

    public static function getAllComponentTypes(): array
    {
        return [
            self::PAYMENT_INVOICE,
            self::THIRD_EXPIRATION,
            self::AUTHENTICATION_REQUEST,
            self::TRANSFER_REFUND,
            self::EXPIRATION_NOTICE,
            self::IDENTITY_VERIFICATION_IN_PROCESS,
            self::IDENTITY_VERIFICATION_VERIFIED,
            self::OTP_VERIFICATION,
            self::ADD_ACCOUNT_CREDIT,
            self::BANK_TRANSFER_NOTIFICATION,
            self::DOMAIN_REDEMPTION_NOTICE,
            self::REPORT_ABUSE,
            self::CLIENTS_QUERY,
            self::DOMAIN_TRANSFER_REQUEST_INITIATED,
            self::USER_INVITE,
            self::DOMAIN_REDEMPTION_PERIOD,
            self::DOMAIN_REGISTRATION_REFUND,
            self::DEFAULT,
        ];
    }

    public static function getAllEmailTypes(): array
    {
        return array_keys(self::EMAIL_TYPE_MAPPING);
    }
}
