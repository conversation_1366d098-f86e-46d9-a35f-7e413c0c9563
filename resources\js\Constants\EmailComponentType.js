// Email component type constants - matches backend EmailComponentType.php
export const EMAIL_COMPONENT_TYPES = {
    PAYMENT_INVOICE: 'PaymentInvoice',
    THIRD_EXPIRATION: 'ThirdExpiration',
    AUTHENTICATION_REQUEST: 'AuthenticationRequest',
    TRANSFER_REFUND: 'TransferRefund',
    EXPIRATION_NOTICE: 'ExpirationNotice',
    IDENTITY_VERIFICATION_IN_PROCESS: 'IdentityVerificationInProcess',
    IDENTITY_VERIFICATION_VERIFIED: 'IdentityVerificationVerified',
    OTP_VERIFICATION: 'OTPVerification',
    ADD_ACCOUNT_CREDIT: 'AddAccountCredit',
    BANK_TRANSFER_NOTIFICATION: 'BankTransferNotification',
    DOMAIN_REDEMPTION_NOTICE: 'DomainRedemptionNotice',
    REPORT_ABUSE: 'ReportAbuse',
    CLIENTS_QUERY: 'ClientsQuery',
    DOMAIN_TRANSFER_REQUEST_INITIATED: 'DomainTransferRequestInitiated',
    USER_INVITE: 'UserInvite',
    DOMAIN_REDEMPTION_PERIOD: 'DomainRedemptionPeriod',
    DEFAULT: 'Default',
};

// Email type to component type mapping
export const EMAIL_TYPE_MAPPING = {
    // Payment Invoice patterns
    'Registration Payment Invoice': EMAIL_COMPONENT_TYPES.PAYMENT_INVOICE,
    'Transfer Payment Invoice': EMAIL_COMPONENT_TYPES.PAYMENT_INVOICE,
    'Renewal Payment Invoice': EMAIL_COMPONENT_TYPES.PAYMENT_INVOICE,
    
    // Expiration notices
    'Third Expiration Notice': EMAIL_COMPONENT_TYPES.THIRD_EXPIRATION,
    'First Expiration Notice': EMAIL_COMPONENT_TYPES.EXPIRATION_NOTICE,
    'Second Expiration Notice': EMAIL_COMPONENT_TYPES.EXPIRATION_NOTICE,
    
    // Authentication and transfers
    'Domain Authentication Request': EMAIL_COMPONENT_TYPES.AUTHENTICATION_REQUEST,
    'Domain Transfer Refund': EMAIL_COMPONENT_TYPES.TRANSFER_REFUND,
    'Domain Transfer - Outbound Request': EMAIL_COMPONENT_TYPES.DOMAIN_TRANSFER_REQUEST_INITIATED,
    
    // Identity verification
    'In Process Identity Verification Notice': EMAIL_COMPONENT_TYPES.IDENTITY_VERIFICATION_IN_PROCESS,
    'Verified Identity Verification Notice': EMAIL_COMPONENT_TYPES.IDENTITY_VERIFICATION_VERIFIED,
    
    // Account and credits
    'OTP Verification': EMAIL_COMPONENT_TYPES.OTP_VERIFICATION,
    'Account Credit - Added to Account': EMAIL_COMPONENT_TYPES.ADD_ACCOUNT_CREDIT,
    'Account Credit - Bank Transfer Notification': EMAIL_COMPONENT_TYPES.BANK_TRANSFER_NOTIFICATION,
    
    // Domain operations
    'Domain Redemption Notice': EMAIL_COMPONENT_TYPES.DOMAIN_REDEMPTION_NOTICE,
    'Domain Redemption Period': EMAIL_COMPONENT_TYPES.DOMAIN_REDEMPTION_PERIOD,
    
    // Communication
    'Report Abuse': EMAIL_COMPONENT_TYPES.REPORT_ABUSE,
    'Clients Query': EMAIL_COMPONENT_TYPES.CLIENTS_QUERY,
    'User Invite': EMAIL_COMPONENT_TYPES.USER_INVITE,
};

/**
 * Get component type from email type
 * @param {string} emailType - The email type to map
 * @returns {string} - The corresponding component type
 */
export const getComponentType = (emailType) => {
    // Direct match first
    if (EMAIL_TYPE_MAPPING[emailType]) {
        return EMAIL_TYPE_MAPPING[emailType];
    }

    // Pattern matching for partial matches (maintaining backward compatibility)
    if (emailType.includes('Payment Invoice')) {
        return EMAIL_COMPONENT_TYPES.PAYMENT_INVOICE;
    }
    
    if (emailType.includes('Authentication Request')) {
        return EMAIL_COMPONENT_TYPES.AUTHENTICATION_REQUEST;
    }
    
    if (emailType.includes('Transfer Refund')) {
        return EMAIL_COMPONENT_TYPES.TRANSFER_REFUND;
    }
    
    if (emailType.startsWith('First Expiration Notice') || 
        emailType.startsWith('Second Expiration Notice')) {
        return EMAIL_COMPONENT_TYPES.EXPIRATION_NOTICE;
    }

    return EMAIL_COMPONENT_TYPES.DEFAULT;
};

/**
 * Get all available email types for filters
 * @returns {Array} - Array of all email types
 */
export const getAllEmailTypes = () => {
    return Object.keys(EMAIL_TYPE_MAPPING);
};
