<?php

namespace App\Modules\RequestDelete\Services;

use App\Events\DomainHistoryEvent;
use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use App\Modules\Epp\Services\EppDomainService;
use App\Util\Constant\UserDomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function approveDeleteRequestSave($request)
    {
        $data = $request->all();

        // $eppInfo = EppDomainService::instance()->callEppDomainInfo($data['domainId']);
        // $datastoreInfo = EppDomainService::instance()->callDatastoreDomainInfo($data['domainId']);

        EppDomainService::instance()->callEppDomainDelete($data['domainId']);
        EppDomainService::instance()->callDatastoreDomainDelete($data['domainId']);

        self::localDelete($data);

        self::userNotification($data);
        self::userEmailNotification($data);
        self::domainHistory($data);

        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $data['domainId'],
            'deleted_by' => Auth::user()->email,
            'deleted_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    public function rejectDeleteRequestSave($request)
    {
        self::updateDomainDeletionRequestTable($request, 0);

        DB::client()->table('domains')->where('id', $request['domainId'])->update([
            'status' => DomainStatus::ACTIVE,
            'deleted_at' => null,
            'updated_at' => now(),
        ]);
    }

    public function createDeleteRequestSave($request)
    {
        $data = $request->all();

        self::newDomainDelete($data);
        self::domainHistory($data);
        self::localDelete($data, skipUpdate: true);

        self::userNotification($data);
        self::userEmailNotification($data);
    }

    private function localDelete($requestData, bool $skipUpdate = false)
    {
        if (!$skipUpdate) {
            self::updateDomainDeletionRequestTable($requestData);
        }

        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $requestData['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $requestData['domainId'])
            ->update($updates);
    }

    private function updateDomainDeletionRequestTable($requestData, $authID = null)
    {
        $agentID = $authID ?? Auth::id();

        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->exists();

        if (!$exists) {
            return;
        }

        $date = Carbon::parse($requestData['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->update([
                'support_agent_id'   => $agentID,
                'support_agent_name'=> Auth::user()->name . ' (' . Auth::user()->email . ')',
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => $requestData['support_note'] ?? 'Support note not provided',
                'is_refunded'        => $is_refunded,
            ]);
    }

    private function userNotification($requestData)
    {
        $userID = $requestData['userID'];
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) return;

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Approved',
            'message'      => 'Your request to delete the domain "' . $domainName . '" has been approved.',
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function userEmailNotification($requestData)
    {
        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $message = [
            'subject'  => 'Domain Deletion Request Approved',
            'greeting' => 'Greetings!',
            'body'     => 'Your request to delete the domain "' . $domainName . '" has been approved.',
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));
    }

    private function newDomainDelete($data)
    {
        $date = Carbon::parse($data['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $data['userID'],
            'domain_id'           => $data['domainId'],
            'reason'              => $data['reason'],
            'support_agent_id'    => Auth::id(),
            'support_agent_name'  => Auth::user()->name . ' (' . Auth::user()->email . ')',
            'deleted_at'          => now(),
            'feedback_date'       => now(),
            'support_note'        => Auth::user()->name . ' (' . Auth::user()->email . ') deleted domain: ' . $data['domainName'],
            'is_refunded'         => $is_refunded,
        ]);
    }

    private function domainHistory($data)
    {
        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $data['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $data['userID'],
            'status'    => 'success',
            'message'   => 'Domain "' . $data['domainName'] . '" deleted by ' . Auth::user()->name . ' (' . Auth::user()->email . ')',
            'payload'   => json_encode($data),
        ]);
    }
}
